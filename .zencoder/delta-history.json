{"snapshots": {"/terminal_output": {"filePath": "/terminal_output", "baseContent": "xrgouda@Amrs-MacBook-Air AFA_app % \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n", "baseTimestamp": 1754858649820}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/main.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/main.dart", "baseContent": "import 'dart:io';\nimport 'package:afa_app/app_config/routes.dart';\nimport 'package:firebase_core/firebase_core.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter/services.dart';\nimport 'package:flutter_riverpod/flutter_riverpod.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\nimport 'package:riverpod_context/riverpod_context.dart';\n\nFuture<void> main() async {\n  WidgetsFlutterBinding.ensureInitialized();\n  await Firebase.initializeApp(\n    options: FirebaseOptions(\n      apiKey: Platform.isAndroid\n          ? \"AIzaSyCE9JqVCG_Whw3SY9pQ0S31GGPI2h1ajyc\"\n          : \"AIzaSyB5Ofub3ytPQElCglUR49t3kMdfsgL3xus\",\n      appId: Platform.isAndroid\n          ? \"1:686384903399:android:1ed0ac4fd0502ce2145e42\"\n          : \"1:686384903399:ios:b8d3ee2d026ce2ed145e42\",\n      messagingSenderId: \"686384903399\",\n      projectId: \"arab-fertilizer\",\n    ),\n  );\n\n  runApp(const ProviderScope(child: InheritedConsumer(child: AfaApp())));\n}\n\nclass AfaApp extends StatefulWidget {\n  const AfaApp({super.key});\n\n  @override\n  State<AfaApp> createState() => _AfaAppState();\n}\n\nclass _AfaAppState extends State<AfaApp> {\n  @override\n  Widget build(BuildContext context) {\n    SystemChrome.setPreferredOrientations([\n      DeviceOrientation.portraitUp,\n      DeviceOrientation.portraitDown,\n    ]);\n    debugInvertOversizedImages = true;\n    return ScreenUtilInit(\n      designSize: const Size(430, 932),\n      minTextAdapt: true,\n      builder: (context, child) => MaterialApp(\n        debugShowCheckedModeBanner: false,\n        theme: ThemeData(\n          fontFamily: \"Roboto\",\n          scaffoldBackgroundColor: Colors.white,\n        ),\n        initialRoute: PATHS.splashScreen,\n        routes: routes,\n      ),\n    );\n  }\n}\n", "baseTimestamp": 1754858823245}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/notification_handler.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/notification_handler.dart", "baseContent": "import 'package:firebase_messaging/firebase_messaging.dart';\nimport 'package:flutter_local_notifications/flutter_local_notifications.dart';\n\nclass NotificationHandler {\n  static final _notification = FlutterLocalNotificationsPlugin();\n  static void init() async {\n    await FirebaseMessaging.instance.requestPermission();\n    _notification.initialize(\n      const InitializationSettings(\n        android: AndroidInitializationSettings('@mipmap/ic_launcher'),\n        iOS: DarwinInitializationSettings(),\n      ),\n      onDidReceiveNotificationResponse: (data) {},\n    );\n  }\n\n  static Future<void> pushNotification(RemoteMessage message) async {\n    const androidNotificationDetails = AndroidNotificationDetails(\n      \"high_importance_channelss\",\n      'High Importance Notifications',\n      channelDescription: 'channel description',\n      importance: Importance.max,\n      priority: Priority.high,\n      enableVibration: true,\n      playSound: true,\n    );\n\n    const darwinNotificationDetails = DarwinNotificationDetails(\n      presentAlert: true,\n      presentBadge: true,\n      presentSound: true,\n    );\n\n    const notificationDetails = NotificationDetails(\n      android: androidNotificationDetails,\n      iOS: darwinNotificationDetails,\n    );\n\n    await _notification.show(\n      0,\n      message.notification!.title,\n      message.notification!.body,\n      notificationDetails,\n    );\n  }\n}\n", "baseTimestamp": 1754858823277}, "/Users/<USER>/Flutter-Projects/AFA_app/pubspec.yaml": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/pubspec.yaml", "baseContent": "name: afa_app\ndescription: \"A new Flutter project.\"\n# The following line prevents the package from being accidentally published to\n# pub.dev using `flutter pub publish`. This is preferred for private packages.\npublish_to: 'none' # Remove this line if you wish to publish to pub.dev\n\n# The following defines the version and build number for your application.\n# A version number is three numbers separated by dots, like 1.2.43\n# followed by an optional build number separated by a +.\n# Both the version and the builder number may be overridden in flutter\n# build by specifying --build-name and --build-number, respectively.\n# In Android, build-name is used as versionName while build-number used as versionCode.\n# Read more about Android versioning at https://developer.android.com/studio/publish/versioning\n# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.\n# Read more about iOS versioning at\n# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html\n# In Windows, build-name is used as the major, minor, and patch parts\n# of the product and file versions while build-number is used as the build suffix.\nversion: 4.0.0+4\n\nenvironment:\n  sdk: ^3.8.1\n\n# Dependencies specify other packages that your package needs in order to work.\n# To automatically upgrade your package dependencies to the latest versions\n# consider running `flutter pub upgrade --major-versions`. Alternatively,\n# dependencies can be manually updated by changing the version numbers below to\n# the latest version available on pub.dev. To see which dependencies have newer\n# versions available, run `flutter pub outdated`.\ndependencies:\n  \n\n  # The following adds the Cupertino Icons font to your application.\n  # Use with the CupertinoIcons class for iOS style icons.\n  \n  badges: ^3.1.2\n  cached_network_image: ^3.4.1\n  connectivity_plus: ^6.1.4\n  cupertino_icons: ^1.0.8\n  dropdown_search: ^6.0.2\n  firebase_core: ^3.15.2\n  firebase_messaging: ^15.2.10\n  flutter:\n    sdk: flutter\n  flutter_lints: ^6.0.0\n  flutter_local_notifications: ^19.3.1\n  flutter_polls: ^0.1.6\n  flutter_riverpod: ^2.6.1\n  flutter_screenutil: ^5.9.3\n  google_maps_flutter: ^2.12.3\n  http: ^1.4.0\n  image_picker: ^1.1.2\n  intl: ^0.20.2\n  permission_handler: ^12.0.1\n  pin_code_fields: ^8.0.1\n  pull_to_refresh: ^2.0.0\n  riverpod_context: ^0.3.0\n  saver_gallery: ^4.0.1\n  shared_preferences: ^2.5.3\n  uid: ^0.0.2\n  url_launcher: ^6.3.1\n  \n\n\n  \n  \n  \n\ndev_dependencies:\n  flutter_test:\n    sdk: flutter\n\n  # The \"flutter_lints\" package below contains a set of recommended lints to\n  # encourage good coding practices. The lint set provided by the package is\n  # activated in the `analysis_options.yaml` file located at the root of your\n  # package. See that file for information about deactivating specific lint\n  # rules and activating additional ones.\n  \n\n# For information on the generic Dart part of this file, see the\n# following page: https://dart.dev/tools/pub/pubspec\n\n# The following section is specific to Flutter packages.\nflutter:\n\n  # The following line ensures that the Material Icons font is\n  # included with your application, so that you can use the icons in\n  # the material Icons class.\n  uses-material-design: true\n\n  # To add assets to your application, add an assets section, like this:\n  assets:\n    - assets/images/\n  #   - images/a_dot_ham.jpeg\n\n  # An image asset can refer to one or more resolution-specific \"variants\", see\n  # https://flutter.dev/to/resolution-aware-images\n\n  # For details regarding adding assets from package dependencies, see\n  # https://flutter.dev/to/asset-from-package\n\n  # To add custom fonts to your application, add a fonts section here,\n  # in this \"flutter\" section. Each entry in this list should have a\n  # \"family\" key with the font family name, and a \"fonts\" key with a\n  # list giving the asset and other descriptors for the font. For\n  # example:\n  fonts:\n    - family: Roboto\n      fonts:\n        - asset: assets/font/Roboto-Regular.ttf\n        - asset: assets/font/Roboto-Bold.ttf\n          weight: 500\n  #   - family: Trajan Pro\n  #     fonts:\n  #       - asset: fonts/TrajanPro.ttf\n  #       - asset: fonts/TrajanPro_Bold.ttf\n  #         weight: 700\n  #\n  # For details regarding fonts from package dependencies,\n  # see https://flutter.dev/to/font-from-package\n", "baseTimestamp": 1754858828779, "deltas": [{"timestamp": 1754858859367, "changes": [{"type": "MODIFY", "lineNumber": 18, "content": "version: 1.16.5+165", "oldContent": "version: 4.0.0+4"}]}, {"timestamp": 1754858864593, "changes": [{"type": "MODIFY", "lineNumber": 18, "content": "version: 1.17.5+165", "oldContent": "version: 1.16.5+165"}]}, {"timestamp": 1754858866935, "changes": [{"type": "MODIFY", "lineNumber": 18, "content": "version: 1.17.0+165", "oldContent": "version: 1.17.5+165"}]}, {"timestamp": 1754858871240, "changes": [{"type": "MODIFY", "lineNumber": 18, "content": "version: 1.17.0+170", "oldContent": "version: 1.17.0+165"}]}]}, "/Dummy.txt": {"filePath": "/Dummy.txt", "baseContent": "Updates", "baseTimestamp": 1754858830618}, "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/home_screen.dart": {"filePath": "/Users/<USER>/Flutter-Projects/AFA_app/lib/screens/home_screens/home_screen.dart", "baseContent": "import 'dart:async';\nimport 'package:afa_app/app_config/app_colors.dart';\nimport 'package:afa_app/app_config/common_components.dart';\nimport 'package:afa_app/widgets/home_screens_widgets.dart/home_screen_widgets.dart';\nimport 'package:flutter/material.dart';\nimport 'package:flutter_screenutil/flutter_screenutil.dart';\n\nclass HomeScreen extends StatefulWidget {\n  const HomeScreen({super.key});\n\n  @override\n  State<HomeScreen> createState() => _HomeScreenState();\n}\n\nclass _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {\n  Timer? timer;\n  Duration difference = const Duration();\n  bool _isTimerActive = false;\n\n  @override\n  void initState() {\n    super.initState();\n    WidgetsBinding.instance.addObserver(this);\n    _startTimer();\n  }\n\n  @override\n  void didChangeAppLifecycleState(AppLifecycleState state) {\n    super.didChangeAppLifecycleState(state);\n    switch (state) {\n      case AppLifecycleState.resumed:\n        if (!_isTimerActive) {\n          _startTimer();\n        }\n        break;\n      case AppLifecycleState.paused:\n      case AppLifecycleState.inactive:\n        _stopTimer();\n        break;\n      case AppLifecycleState.detached:\n        _stopTimer();\n        break;\n      case AppLifecycleState.hidden:\n        _stopTimer();\n        break;\n    }\n  }\n\n  void _startTimer() {\n    if (_isTimerActive) return;\n\n    _isTimerActive = true;\n    timer = Timer.periodic(const Duration(seconds: 1), (timer) {\n      if (!mounted) {\n        _stopTimer();\n        return;\n      }\n\n      try {\n        final now = DateTime.now();\n        // Target date: September 16th, 2024 at 12:00 PM\n        final targetDate = DateTime(2024, 9, 16, 12, 0, 0);\n\n        final newDifference = targetDate.difference(now);\n\n        if (mounted) {\n          setState(() {\n            difference = newDifference;\n          });\n        }\n      } catch (e) {\n        // Handle any errors in timer calculation\n        debugPrint('Timer calculation error: $e');\n        if (mounted) {\n          setState(() {\n            difference = const Duration();\n          });\n        }\n      }\n    });\n  }\n\n  void _stopTimer() {\n    if (timer != null && timer!.isActive) {\n      timer!.cancel();\n    }\n    _isTimerActive = false;\n  }\n\n  List<Map<String, dynamic>> formatDuration(Duration d) {\n    // Handle negative durations (when event has passed)\n    final isNegative = d.isNegative;\n    final absoluteDuration = isNegative ? -d : d;\n\n    final days = absoluteDuration.inDays;\n    final hours = absoluteDuration.inHours % 24;\n    final minutes = absoluteDuration.inMinutes % 60;\n    final seconds = absoluteDuration.inSeconds % 60;\n\n    final List<Map<String, dynamic>> eventTimer = [\n      {\"time\": isNegative ? 0 : days, \"title\": \"Days\"},\n      {\"time\": isNegative ? 0 : hours, \"title\": \"Hours\"},\n      {\"time\": isNegative ? 0 : minutes, \"title\": \"Minutes\"},\n      {\"time\": isNegative ? 0 : seconds, \"title\": \"Seconds\"},\n    ];\n    return eventTimer;\n  }\n\n  @override\n  void dispose() {\n    WidgetsBinding.instance.removeObserver(this);\n    _stopTimer();\n    super.dispose();\n  }\n\n  @override\n  Widget build(BuildContext context) {\n    return Column(\n      crossAxisAlignment: CrossAxisAlignment.start,\n      children: [\n        Center(\n          child: HomeScreenWidgets.headerScreenWidget(\n            context: context,\n            eventTimer: formatDuration(difference!),\n          ),\n        ),\n\n        SizedBox(height: 35.0.h),\n        GridView.builder(\n          padding: EdgeInsets.all(10.0.h),\n          shrinkWrap: true,\n          itemCount: HomeScreenWidgets.homFiledsList.length,\n          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(\n            crossAxisCount: 4,\n            mainAxisExtent: 100.0.h,\n            mainAxisSpacing: 20.0.h,\n          ),\n\n          itemBuilder: (context, index) => InkWell(\n            onTap: () {\n              Navigator.pushNamed(\n                context,\n                HomeScreenWidgets.homFiledsList[index]['path'],\n              );\n            },\n            child: Center(\n              child: Column(\n                children: [\n                  Container(\n                    padding: EdgeInsets.all(15.0.h),\n                    decoration: const BoxDecoration(\n                      shape: BoxShape.circle,\n                      color: AppColors.darkGreenColor,\n                    ),\n                    child: CommonComponents.imageAssetWithCache(\n                      context: context,\n                      image: HomeScreenWidgets.homFiledsList[index]['image'],\n                      height: 36.0.h,\n                      width: 36.0.w,\n                      fit: BoxFit.contain,\n                    ),\n                  ),\n                  SizedBox(height: 10.0.h),\n                  Text(\n                    HomeScreenWidgets.homFiledsList[index]['title'],\n                    style: TextStyle(\n                      fontSize: 12.0.sp,\n                      fontWeight: FontWeight.bold,\n                      color: AppColors.blackColor,\n                    ),\n                  ),\n                ],\n              ),\n            ),\n          ),\n        ),\n      ],\n    );\n  }\n}\n", "baseTimestamp": 1754859876966, "deltas": [{"timestamp": 1754859887174, "changes": [{"type": "MODIFY", "lineNumber": 123, "content": "            eventTimer: formatDuration(difference),", "oldContent": "            eventTimer: formatDuration(difference!),"}]}, {"timestamp": 1754860186364, "changes": [{"type": "MODIFY", "lineNumber": 14, "content": "class _HomeScreenState extends State<HomeScreen> {", "oldContent": "class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {"}, {"type": "DELETE", "lineNumber": 16, "oldContent": "  Duration difference = const Duration();"}, {"type": "DELETE", "lineNumber": 17, "oldContent": "  bool _isTimerActive = false;"}, {"type": "INSERT", "lineNumber": 16, "content": "  Duration? difference = const Duration();"}, {"type": "DELETE", "lineNumber": 21, "oldContent": "    super.initState();"}, {"type": "DELETE", "lineNumber": 22, "oldContent": "    WidgetsBinding.instance.addObserver(this);"}, {"type": "INSERT", "lineNumber": 21, "content": "    super.initState();"}, {"type": "DELETE", "lineNumber": 26, "oldContent": "  @override"}, {"type": "DELETE", "lineNumber": 27, "oldContent": "  void didChangeAppLifecycleState(AppLifecycleState state) {"}, {"type": "DELETE", "lineNumber": 28, "oldContent": "    super.didChangeAppLifecycleState(state);"}, {"type": "DELETE", "lineNumber": 29, "oldContent": "    switch (state) {"}, {"type": "DELETE", "lineNumber": 30, "oldContent": "      case AppLifecycleState.resumed:"}, {"type": "DELETE", "lineNumber": 31, "oldContent": "        if (!_isTimerActive) {"}, {"type": "DELETE", "lineNumber": 32, "oldContent": "          _startTimer();"}, {"type": "DELETE", "lineNumber": 33, "oldContent": "        }"}, {"type": "DELETE", "lineNumber": 34, "oldContent": "        break;"}, {"type": "DELETE", "lineNumber": 35, "oldContent": "      case AppLifecycleState.paused:"}, {"type": "DELETE", "lineNumber": 36, "oldContent": "      case AppLifecycleState.inactive:"}, {"type": "DELETE", "lineNumber": 37, "oldContent": "        _stopTimer();"}, {"type": "DELETE", "lineNumber": 38, "oldContent": "        break;"}, {"type": "DELETE", "lineNumber": 39, "oldContent": "      case AppLifecycleState.detached:"}, {"type": "DELETE", "lineNumber": 40, "oldContent": "        _stopTimer();"}, {"type": "DELETE", "lineNumber": 41, "oldContent": "        break;"}, {"type": "DELETE", "lineNumber": 42, "oldContent": "      case AppLifecycleState.hidden:"}, {"type": "DELETE", "lineNumber": 43, "oldContent": "        _stopTimer();"}, {"type": "DELETE", "lineNumber": 44, "oldContent": "        break;"}, {"type": "DELETE", "lineNumber": 45, "oldContent": "    }"}, {"type": "DELETE", "lineNumber": 46, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 47, "oldContent": ""}, {"type": "DELETE", "lineNumber": 49, "oldContent": "    if (_isTimerActive) return;"}, {"type": "INSERT", "lineNumber": 25, "content": "    timer = Timer.periodic(const Duration(seconds: 1), (time) {"}, {"type": "INSERT", "lineNumber": 26, "content": "      final now = DateTime.now();"}, {"type": "DELETE", "lineNumber": 51, "oldContent": "    _isTimerActive = true;"}, {"type": "DELETE", "lineNumber": 52, "oldContent": "    timer = Timer.periodic(const Duration(seconds: 1), (timer) {"}, {"type": "DELETE", "lineNumber": 53, "oldContent": "      if (!mounted) {"}, {"type": "DELETE", "lineNumber": 54, "oldContent": "        _stopTimer();"}, {"type": "DELETE", "lineNumber": 55, "oldContent": "        return;"}, {"type": "DELETE", "lineNumber": 56, "oldContent": "      }"}, {"type": "INSERT", "lineNumber": 28, "content": "      final targetDate = DateTime(now.year, 9, 16, 12, 0, 0);"}, {"type": "DELETE", "lineNumber": 58, "oldContent": "      try {"}, {"type": "DELETE", "lineNumber": 59, "oldContent": "        final now = DateTime.now();"}, {"type": "DELETE", "lineNumber": 60, "oldContent": "        // Target date: September 16th, 2024 at 12:00 PM"}, {"type": "DELETE", "lineNumber": 61, "oldContent": "        final targetDate = DateTime(2024, 9, 16, 12, 0, 0);"}, {"type": "DELETE", "lineNumber": 62, "oldContent": ""}, {"type": "DELETE", "lineNumber": 63, "oldContent": "        final newDifference = targetDate.difference(now);"}, {"type": "DELETE", "lineNumber": 64, "oldContent": ""}, {"type": "DELETE", "lineNumber": 65, "oldContent": "        if (mounted) {"}, {"type": "DELETE", "lineNumber": 66, "oldContent": "          setState(() {"}, {"type": "DELETE", "lineNumber": 67, "oldContent": "            difference = newDifference;"}, {"type": "DELETE", "lineNumber": 68, "oldContent": "          });"}, {"type": "DELETE", "lineNumber": 69, "oldContent": "        }"}, {"type": "DELETE", "lineNumber": 70, "oldContent": "      } catch (e) {"}, {"type": "DELETE", "lineNumber": 71, "oldContent": "        // Handle any errors in timer calculation"}, {"type": "DELETE", "lineNumber": 72, "oldContent": "        debugPrint('Timer calculation error: $e');"}, {"type": "DELETE", "lineNumber": 73, "oldContent": "        if (mounted) {"}, {"type": "DELETE", "lineNumber": 74, "oldContent": "          setState(() {"}, {"type": "DELETE", "lineNumber": 75, "oldContent": "            difference = const Duration();"}, {"type": "DELETE", "lineNumber": 76, "oldContent": "          });"}, {"type": "DELETE", "lineNumber": 77, "oldContent": "        }"}, {"type": "DELETE", "lineNumber": 78, "oldContent": "      }"}, {"type": "INSERT", "lineNumber": 30, "content": "      setState(() {"}, {"type": "INSERT", "lineNumber": 31, "content": "        difference = targetDate.difference(now);"}, {"type": "INSERT", "lineNumber": 32, "content": "      });"}, {"type": "DELETE", "lineNumber": 82, "oldContent": "  void _stopTimer() {"}, {"type": "DELETE", "lineNumber": 83, "oldContent": "    if (timer != null && timer!.isActive) {"}, {"type": "DELETE", "lineNumber": 84, "oldContent": "      timer!.cancel();"}, {"type": "DELETE", "lineNumber": 85, "oldContent": "    }"}, {"type": "DELETE", "lineNumber": 86, "oldContent": "    _isTimerActive = false;"}, {"type": "DELETE", "lineNumber": 87, "oldContent": "  }"}, {"type": "DELETE", "lineNumber": 88, "oldContent": ""}, {"type": "DELETE", "lineNumber": 90, "oldContent": "    // Handle negative durations (when event has passed)"}, {"type": "DELETE", "lineNumber": 91, "oldContent": "    final isNegative = d.isNegative;"}, {"type": "DELETE", "lineNumber": 92, "oldContent": "    final absoluteDuration = isNegative ? -d : d;"}, {"type": "INSERT", "lineNumber": 37, "content": "    final days = d.inDays;"}, {"type": "INSERT", "lineNumber": 38, "content": "    final hours = d.inDays % 24;"}, {"type": "INSERT", "lineNumber": 39, "content": "    final minutes = d.in<PERSON><PERSON> % 60;"}, {"type": "INSERT", "lineNumber": 40, "content": "    final seconds = d.in<PERSON><PERSON><PERSON><PERSON> % 60;"}, {"type": "DELETE", "lineNumber": 94, "oldContent": "    final days = absoluteDuration.inDays;"}, {"type": "DELETE", "lineNumber": 95, "oldContent": "    final hours = absoluteDuration.inHours % 24;"}, {"type": "DELETE", "lineNumber": 96, "oldContent": "    final minutes = absoluteDuration.inMinutes % 60;"}, {"type": "DELETE", "lineNumber": 97, "oldContent": "    final seconds = absoluteDuration.inSeconds % 60;"}, {"type": "DELETE", "lineNumber": 98, "oldContent": ""}, {"type": "DELETE", "lineNumber": 100, "oldContent": "      {\"time\": isNegative ? 0 : days, \"title\": \"Days\"},"}, {"type": "DELETE", "lineNumber": 101, "oldContent": "      {\"time\": isNegative ? 0 : hours, \"title\": \"Hours\"},"}, {"type": "DELETE", "lineNumber": 102, "oldContent": "      {\"time\": isNegative ? 0 : minutes, \"title\": \"Minutes\"},"}, {"type": "DELETE", "lineNumber": 103, "oldContent": "      {\"time\": isNegative ? 0 : seconds, \"title\": \"Seconds\"},"}, {"type": "INSERT", "lineNumber": 43, "content": "      {\"time\": days, \"title\": \"Days\"},"}, {"type": "INSERT", "lineNumber": 44, "content": "      {\"time\": hours - 6, \"title\": \"Hours\"},"}, {"type": "INSERT", "lineNumber": 45, "content": "      {\"time\": minutes, \"title\": \"Minute\"},"}, {"type": "INSERT", "lineNumber": 46, "content": "      {\"time\": seconds, \"title\": \"Seconds\"},"}, {"type": "DELETE", "lineNumber": 110, "oldContent": "    WidgetsBinding.instance.removeObserver(this);"}, {"type": "DELETE", "lineNumber": 111, "oldContent": "    _stopTimer();"}, {"type": "INSERT", "lineNumber": 53, "content": "    timer!.cancel();"}, {"type": "DELETE", "lineNumber": 123, "oldContent": "            eventTimer: formatDuration(difference),"}, {"type": "INSERT", "lineNumber": 65, "content": "            eventTimer: formatDuration(difference!),"}]}]}}}